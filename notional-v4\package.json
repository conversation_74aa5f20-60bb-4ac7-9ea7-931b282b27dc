{"name": "@notional-finance/notional-v4", "description": "", "version": "1.0.0", "author": {"name": "notional-finance", "url": "https://github.com/notional-finance"}, "dependencies": {"@openzeppelin/contracts": "^5.2.0"}, "devDependencies": {"forge-std": "github:foundry-rs/forge-std#v1.9.6", "prettier": "^3.0.0", "solhint": "^3.6.2"}, "keywords": ["blockchain", "ethereum", "forge", "foundry", "smart-contracts", "solidity", "template"], "private": true, "scripts": {"clean": "rm -rf cache out", "build": "forge build", "lint": "bun run lint:sol && bun run prettier:check", "lint:sol": "forge fmt --check && bun solhint \"{script,src,tests}/**/*.sol\"", "prettier:check": "prettier --check \"**/*.{json,md,yml}\" --ignore-path \".prettierignore\"", "prettier:write": "prettier --write \"**/*.{json,md,yml}\" --ignore-path \".prettierignore\"", "test": "forge test", "test:coverage": "forge coverage", "test:coverage:report": "forge coverage --report lcov && genhtml lcov.info --branch-coverage --output-dir coverage"}}