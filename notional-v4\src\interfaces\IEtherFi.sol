// SPDX-License-Identifier: BUSL-1.1
pragma solidity >=0.8.29;

import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";

interface IweETH is IERC20 {
    function wrap(uint256 eETHDeposit) external returns (uint256 weETHMinted);
    function unwrap(uint256 weETHDeposit) external returns (uint256 eETHMinted);
}

interface ILiquidityPool {
    function deposit() external payable returns (uint256 eETHMinted);
    function requestWithdraw(address requester, uint256 eETHAmount) external returns (uint256 requestId);
}

interface IWithdrawRequestNFT {
    function ownerOf(uint256 requestId) external view returns (address);
    function isFinalized(uint256 requestId) external view returns (bool);
    function getClaimableAmount(uint256 requestId) external view returns (uint256);
    function claimWithdraw(uint256 requestId) external;
    function finalizeRequests(uint256 requestId) external;
}

IweETH constant weETH = IweETH(******************************************);
ERC20 constant eETH = ERC20(******************************************);
ILiquidityPool constant LiquidityPool = ILiquidityPool(******************************************);
IWithdrawRequestNFT constant WithdrawRequestNFT = IWithdrawRequestNFT(******************************************);