# Full reference https://github.com/foundry-rs/foundry/tree/master/crates/config

[profile.default]
  auto_detect_solc = false
  block_timestamp = 1_738_368_000 # Feb 1, 2025 at 00:00 GMT
  bytecode_hash = "none"
  evm_version = "cancun"
  fuzz = { runs = 1_000 }
  gas_reports = ["*"]
  optimizer = true
  optimizer_runs = 10_000
  out = "out"
  script = "script"
  solc = "0.8.29"
  src = "src"
  test = "tests"

[profile.ci]
  fuzz = { runs = 10_000 }
  verbosity = 4

[etherscan]
  mainnet = { key = "${API_KEY_ETHERSCAN}" }

[fmt]
  bracket_spacing = true
  int_types = "long"
  line_length = 120
  multiline_func_header = "all"
  number_underscore = "thousands"
  quote_style = "double"
  tab_width = 4
  wrap_comments = true

[rpc_endpoints]
  arbitrum = "https://arbitrum-one-rpc.publicnode.com"
  avalanche = "https://avalanche-c-chain-rpc.publicnode.com"
  base = "https://mainnet.base.org"
  bnb_smart_chain = "https://bsc-dataseed.binance.org"
  gnosis_chain = "https://rpc.gnosischain.com"
  localhost = "http://localhost:8545"
  mainnet = "https://eth-mainnet.g.alchemy.com/v2/${API_KEY_ALCHEMY}"
  optimism = "https://optimism-rpc.publicnode.com"
  polygon = "https://polygon-bor-rpc.publicnode.com"
  sepolia = "https://ethereum-sepolia-rpc.publicnode.com"

[profile.libraries]
"src/staking/PendlePTLib.sol:PendlePTLib" = "******************************************"
